import React from 'react';
import {
  View,
  StyleSheet,
  ImageBackground,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import {
  Text,
  Button,
  Surface,
  Divider,
} from 'react-native-paper';
import { useRoute, useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

import { colors, spacing, typography } from '../../constants';
import type { PublicNavigationProp } from '../../navigation/PublicNavigator';
import type { Room } from '../../types/database';

interface RouteParams {
  action?: 'booking' | 'reservation' | 'profile';
  roomId?: string;
  room?: Room;
  checkIn?: string;
  checkOut?: string;
  guests?: number;
}

export const AuthPromptScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const params = (route.params as RouteParams) || {};

  const getActionText = () => {
    switch (params?.action) {
      case 'booking':
        return {
          title: 'Sign In to Book',
          subtitle: 'Create an account or sign in to make your reservation',
          icon: 'bed-outline',
        };
      case 'reservation':
        return {
          title: 'Sign In to View Reservations',
          subtitle: 'Access your booking history and manage reservations',
          icon: 'calendar-outline',
        };
      case 'profile':
        return {
          title: 'Sign In to Your Account',
          subtitle: 'Manage your profile and preferences',
          icon: 'person-outline',
        };
      default:
        return {
          title: 'Sign In Required',
          subtitle: 'Please sign in to continue',
          icon: 'lock-closed-outline',
        };
    }
  };

  const handleSignInPress = () => {
    // Navigate to Auth stack with modal presentation
    (navigation as any).navigate('Auth', {
      screen: 'Login',
      params: {
        returnTo: 'booking',
        roomId: params?.roomId,
        room: params?.room,
        checkIn: params?.checkIn,
        checkOut: params?.checkOut,
        guests: params?.guests,
      }
    });
  };

  const handleSignUpPress = () => {
    // Navigate to Auth stack with modal presentation
    (navigation as any).navigate('Auth', {
      screen: 'Register',
      params: {
        returnTo: 'booking',
        roomId: params?.roomId,
        room: params?.room,
        checkIn: params?.checkIn,
        checkOut: params?.checkOut,
        guests: params?.guests,
      }
    });
  };

  const handleContinueBrowsing = () => {
    navigation.goBack();
  };

  const actionText = getActionText();

  return (
    <ImageBackground
      source={{
        uri: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80'
      }}
      style={styles.background}
    >
      <StatusBar barStyle="light-content" />
      <LinearGradient
        colors={['rgba(0,0,0,0.4)', 'rgba(0,0,0,0.8)']}
        style={styles.overlay}
      >
        <SafeAreaView style={styles.container}>
          <View style={styles.content}>
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.iconContainer}>
                <Ionicons 
                  name={actionText.icon as any} 
                  size={64} 
                  color={colors.primary} 
                />
              </View>
              <Text style={styles.title}>{actionText.title}</Text>
              <Text style={styles.subtitle}>{actionText.subtitle}</Text>
            </View>

            {/* Room Info (if booking) */}
            {params.action === 'booking' && params.room && (
              <Surface style={styles.roomInfo}>
                <Text style={styles.roomInfoTitle}>Selected Room</Text>
                <Text style={styles.roomName}>Room {params.room.room_number}</Text>
                <Text style={styles.roomType}>
                  {params.room.room_type.charAt(0).toUpperCase() + params.room.room_type.slice(1)} Room
                </Text>
                <Text style={styles.roomPrice}>
                  KSh {params.room.price_per_night.toLocaleString()} per night
                </Text>
              </Surface>
            )}

            {/* Benefits */}
            <Surface style={styles.benefitsContainer}>
              <Text style={styles.benefitsTitle}>Why Sign In?</Text>
              <View style={styles.benefitsList}>
                <View style={styles.benefitItem}>
                  <Ionicons name="checkmark-circle" size={20} color={colors.success} />
                  <Text style={styles.benefitText}>Secure booking process</Text>
                </View>
                <View style={styles.benefitItem}>
                  <Ionicons name="checkmark-circle" size={20} color={colors.success} />
                  <Text style={styles.benefitText}>Manage your reservations</Text>
                </View>
                <View style={styles.benefitItem}>
                  <Ionicons name="checkmark-circle" size={20} color={colors.success} />
                  <Text style={styles.benefitText}>Instant booking confirmation</Text>
                </View>
                <View style={styles.benefitItem}>
                  <Ionicons name="checkmark-circle" size={20} color={colors.success} />
                  <Text style={styles.benefitText}>Special offers & discounts</Text>
                </View>
              </View>
            </Surface>

            {/* Action Buttons */}
            <View style={styles.buttonContainer}>
              <Button
                mode="contained"
                onPress={handleSignInPress}
                style={styles.primaryButton}
                contentStyle={styles.buttonContent}
              >
                Sign In
              </Button>

              <Button
                mode="outlined"
                onPress={handleSignUpPress}
                style={styles.secondaryButton}
                contentStyle={styles.buttonContent}
                textColor={colors.onSurface}
              >
                Create Account
              </Button>

              <Divider style={styles.divider} />

              <Button
                mode="text"
                onPress={handleContinueBrowsing}
                textColor={colors.onSurfaceVariant}
              >
                Continue Browsing
              </Button>
            </View>

            {/* Footer */}
            <View style={styles.footer}>
              <Text style={styles.footerText}>
                By signing in, you agree to our Terms of Service and Privacy Policy
              </Text>
            </View>
          </View>
        </SafeAreaView>
      </LinearGradient>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  background: {
    flex: 1,
  },
  overlay: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: spacing.lg,
    justifyContent: 'center',
    gap: spacing.xl,
  },
  header: {
    alignItems: 'center',
    gap: spacing.md,
  },
  iconContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  title: {
    fontSize: typography.sizes.xxl,
    fontWeight: 'bold',
    color: colors.onPrimary,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: typography.sizes.md,
    color: colors.onPrimary,
    textAlign: 'center',
    opacity: 0.9,
  },
  roomInfo: {
    padding: spacing.lg,
    borderRadius: 16,
    alignItems: 'center',
    gap: spacing.sm,
  },
  roomInfoTitle: {
    fontSize: typography.sizes.sm,
    color: colors.onSurfaceVariant,
    fontWeight: '500',
  },
  roomName: {
    fontSize: typography.sizes.lg,
    fontWeight: 'bold',
    color: colors.onSurface,
  },
  roomType: {
    fontSize: typography.sizes.md,
    color: colors.onSurfaceVariant,
  },
  roomPrice: {
    fontSize: typography.sizes.lg,
    fontWeight: 'bold',
    color: colors.primary,
  },
  benefitsContainer: {
    padding: spacing.lg,
    borderRadius: 16,
  },
  benefitsTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: 'bold',
    color: colors.onSurface,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  benefitsList: {
    gap: spacing.md,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  benefitText: {
    fontSize: typography.sizes.md,
    color: colors.onSurface,
  },
  buttonContainer: {
    gap: spacing.md,
  },
  primaryButton: {
    borderRadius: 12,
  },
  secondaryButton: {
    borderRadius: 12,
    borderColor: colors.outline,
  },
  buttonContent: {
    paddingVertical: spacing.sm,
  },
  divider: {
    marginVertical: spacing.sm,
  },
  footer: {
    alignItems: 'center',
  },
  footerText: {
    fontSize: typography.sizes.xs,
    color: colors.onSurfaceVariant,
    textAlign: 'center',
    opacity: 0.8,
  },
});
